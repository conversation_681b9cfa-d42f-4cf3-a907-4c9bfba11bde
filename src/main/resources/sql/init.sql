-- 会议管理系统数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS `meeting_management` DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE `meeting_management`;

-- 用户表
DROP TABLE IF EXISTS `sys_user`;
CREATE TABLE `sys_user` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `username` varchar(50) NOT NULL COMMENT '用户名',
    `password` varchar(100) NOT NULL COMMENT '密码',
    `nickname` varchar(50) NOT NULL COMMENT '昵称',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
    `avatar` varchar(200) DEFAULT NULL COMMENT '头像URL',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `role` varchar(20) NOT NULL DEFAULT 'USER' COMMENT '角色：ADMIN-管理员，USER-普通用户',
    `dept_id` bigint(20) DEFAULT NULL COMMENT '部门ID',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_username` (`username`),
    UNIQUE KEY `uk_email` (`email`),
    KEY `idx_status` (`status`),
    KEY `idx_role` (`role`),
    KEY `idx_dept_id` (`dept_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 部门表
DROP TABLE IF EXISTS `sys_dept`;
CREATE TABLE `sys_dept` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(50) NOT NULL COMMENT '部门名称',
    `parent_id` bigint(20) DEFAULT '0' COMMENT '父部门ID',
    `order_num` int(11) DEFAULT '0' COMMENT '显示顺序',
    `leader` varchar(50) DEFAULT NULL COMMENT '负责人',
    `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
    `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 会议室表
DROP TABLE IF EXISTS `meeting_room`;
CREATE TABLE `meeting_room` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `name` varchar(100) NOT NULL COMMENT '会议室名称',
    `location` varchar(200) DEFAULT NULL COMMENT '会议室位置',
    `capacity` int(11) NOT NULL DEFAULT '0' COMMENT '容纳人数',
    `equipment` text COMMENT '设备信息（JSON格式）',
    `description` varchar(500) DEFAULT NULL COMMENT '会议室描述',
    `status` tinyint(1) NOT NULL DEFAULT '1' COMMENT '状态：0-禁用，1-启用',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_status` (`status`),
    KEY `idx_capacity` (`capacity`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议室表';

-- 会议表
DROP TABLE IF EXISTS `meeting`;
CREATE TABLE `meeting` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `title` varchar(200) NOT NULL COMMENT '会议标题',
    `description` text COMMENT '会议描述',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime NOT NULL COMMENT '结束时间',
    `room_id` bigint(20) NOT NULL COMMENT '会议室ID',
    `organizer_id` bigint(20) NOT NULL COMMENT '组织者ID',
    `status` varchar(20) NOT NULL DEFAULT 'SCHEDULED' COMMENT '会议状态：SCHEDULED-已安排，ONGOING-进行中，COMPLETED-已完成，CANCELLED-已取消',
    `type` varchar(20) DEFAULT 'INTERNAL' COMMENT '会议类型：INTERNAL-内部会议，EXTERNAL-外部会议',
    `agenda` text COMMENT '会议议程',
    `materials` text COMMENT '会议资料（JSON格式）',
    `recording_url` varchar(500) DEFAULT NULL COMMENT '录音/录像URL',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_end_time` (`end_time`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_organizer_id` (`organizer_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议表';

-- 会议参与者表
DROP TABLE IF EXISTS `meeting_participant`;
CREATE TABLE `meeting_participant` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `meeting_id` bigint(20) NOT NULL COMMENT '会议ID',
    `user_id` bigint(20) NOT NULL COMMENT '用户ID',
    `role` varchar(20) NOT NULL DEFAULT 'PARTICIPANT' COMMENT '参会角色：ORGANIZER-组织者，PARTICIPANT-参与者，OBSERVER-观察者',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '参会状态：PENDING-待确认，ACCEPTED-已接受，DECLINED-已拒绝，ATTENDED-已参加',
    `invited_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '邀请时间',
    `response_time` datetime DEFAULT NULL COMMENT '响应时间',
    `remark` varchar(500) DEFAULT NULL COMMENT '备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    PRIMARY KEY (`id`),
    UNIQUE KEY `uk_meeting_user` (`meeting_id`, `user_id`),
    KEY `idx_meeting_id` (`meeting_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议参与者表';

-- 会议预约记录表
DROP TABLE IF EXISTS `meeting_booking`;
CREATE TABLE `meeting_booking` (
    `id` bigint(20) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
    `room_id` bigint(20) NOT NULL COMMENT '会议室ID',
    `meeting_id` bigint(20) DEFAULT NULL COMMENT '会议ID（如果有关联会议）',
    `user_id` bigint(20) NOT NULL COMMENT '预约用户ID',
    `start_time` datetime NOT NULL COMMENT '开始时间',
    `end_time` datetime NOT NULL COMMENT '结束时间',
    `purpose` varchar(200) DEFAULT NULL COMMENT '预约目的',
    `status` varchar(20) NOT NULL DEFAULT 'PENDING' COMMENT '预约状态：PENDING-待审核，APPROVED-已通过，REJECTED-已拒绝，CANCELLED-已取消',
    `approve_user_id` bigint(20) DEFAULT NULL COMMENT '审核人ID',
    `approve_time` datetime DEFAULT NULL COMMENT '审核时间',
    `approve_remark` varchar(500) DEFAULT NULL COMMENT '审核备注',
    `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    `deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标志：0-未删除，1-已删除',
    PRIMARY KEY (`id`),
    KEY `idx_room_id` (`room_id`),
    KEY `idx_meeting_id` (`meeting_id`),
    KEY `idx_user_id` (`user_id`),
    KEY `idx_start_time` (`start_time`),
    KEY `idx_end_time` (`end_time`),
    KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='会议预约记录表';

-- 插入初始数据

-- 插入部门数据
INSERT INTO `sys_dept` (`id`, `name`, `parent_id`, `order_num`, `leader`, `phone`, `email`, `status`) VALUES
(1, '总公司', 0, 1, '张总', '13800000001', '<EMAIL>', 1),
(2, '技术部', 1, 1, '李部长', '13800000002', '<EMAIL>', 1),
(3, '产品部', 1, 2, '王部长', '13800000003', '<EMAIL>', 1),
(4, '市场部', 1, 3, '刘部长', '13800000004', '<EMAIL>', 1),
(5, '人事部', 1, 4, '陈部长', '13800000005', '<EMAIL>', 1);

-- 插入用户数据（密码均为：123456，经过加密）
INSERT INTO `sys_user` (`id`, `username`, `password`, `nickname`, `email`, `phone`, `role`, `dept_id`, `status`) VALUES
(1, 'admin', '$2a$10$7JB720yubVSOfvVaMWye/eInyQcxiGVyMrzrSMsm8.1sHTEaEvs/6', '系统管理员', '<EMAIL>', '13800000001', 'ADMIN', 1, 1),
(2, 'zhangsan', '$2a$10$7JB720yubVSOfvVaMWye/eInyQcxiGVyMrzrSMsm8.1sHTEaEvs/6', '张三', '<EMAIL>', '13800000002', 'USER', 2, 1),
(3, 'lisi', '$2a$10$7JB720yubVSOfvVaMWye/eInyQcxiGVyMrzrSMsm8.1sHTEaEvs/6', '李四', '<EMAIL>', '13800000003', 'USER', 3, 1),
(4, 'wangwu', '$2a$10$7JB720yubVSOfvVaMWye/eInyQcxiGVyMrzrSMsm8.1sHTEaEvs/6', '王五', '<EMAIL>', '13800000004', 'USER', 4, 1),
(5, 'zhaoliu', '$2a$10$7JB720yubVSOfvVaMWye/eInyQcxiGVyMrzrSMsm8.1sHTEaEvs/6', '赵六', '<EMAIL>', '13800000005', 'USER', 5, 1);

-- 插入会议室数据
INSERT INTO `meeting_room` (`id`, `name`, `location`, `capacity`, `equipment`, `description`, `status`) VALUES
(1, '大会议室A', '1楼101', 20, '["投影仪", "白板", "音响系统", "视频会议设备"]', '可容纳20人的大型会议室，设备齐全', 1),
(2, '中会议室B', '2楼201', 10, '["投影仪", "白板", "音响系统"]', '可容纳10人的中型会议室', 1),
(3, '小会议室C', '2楼202', 6, '["电视", "白板"]', '可容纳6人的小型会议室', 1),
(4, '视频会议室D', '3楼301', 15, '["投影仪", "白板", "音响系统", "视频会议设备", "录音设备"]', '专业视频会议室，支持远程会议', 1),
(5, '培训室E', '3楼302', 30, '["投影仪", "音响系统", "白板", "讲台"]', '大型培训室，适合培训和大型会议', 1);

-- 插入示例会议数据
INSERT INTO `meeting` (`id`, `title`, `description`, `start_time`, `end_time`, `room_id`, `organizer_id`, `status`, `type`, `agenda`) VALUES
(1, '项目启动会议', '新项目启动讨论会议', '2025-08-01 09:00:00', '2025-08-01 11:00:00', 1, 1, 'SCHEDULED', 'INTERNAL', '1. 项目背景介绍\n2. 项目目标确定\n3. 团队分工\n4. 时间节点规划'),
(2, '产品需求评审', '产品新功能需求评审会议', '2025-08-02 14:00:00', '2025-08-02 16:00:00', 2, 3, 'SCHEDULED', 'INTERNAL', '1. 需求文档review\n2. 技术可行性分析\n3. 工作量评估'),
(3, '客户沟通会', '与重要客户的沟通会议', '2025-08-03 10:00:00', '2025-08-03 12:00:00', 4, 4, 'SCHEDULED', 'EXTERNAL', '1. 项目进展汇报\n2. 客户需求确认\n3. 下阶段计划');

-- 插入会议参与者数据
INSERT INTO `meeting_participant` (`meeting_id`, `user_id`, `role`, `status`) VALUES
(1, 1, 'ORGANIZER', 'ACCEPTED'),
(1, 2, 'PARTICIPANT', 'ACCEPTED'),
(1, 3, 'PARTICIPANT', 'PENDING'),
(1, 4, 'PARTICIPANT', 'ACCEPTED'),
(2, 3, 'ORGANIZER', 'ACCEPTED'),
(2, 2, 'PARTICIPANT', 'ACCEPTED'),
(2, 1, 'OBSERVER', 'ACCEPTED'),
(3, 4, 'ORGANIZER', 'ACCEPTED'),
(3, 1, 'PARTICIPANT', 'ACCEPTED'),
(3, 5, 'PARTICIPANT', 'PENDING');
