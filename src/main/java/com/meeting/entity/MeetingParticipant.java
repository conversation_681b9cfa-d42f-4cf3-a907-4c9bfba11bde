package com.meeting.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 会议参与者实体类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting_participant")
public class MeetingParticipant extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议ID
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 参会角色：ORGANIZER-组织者，PARTICIPANT-参与者，OBSERVER-观察者
     */
    @TableField("role")
    private String role;

    /**
     * 参会状态：PENDING-待确认，ACCEPTED-已接受，DECLINED-已拒绝，ATTENDED-已参加
     */
    @TableField("status")
    private String status;

    /**
     * 邀请时间
     */
    @TableField("invited_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime invitedTime;

    /**
     * 响应时间
     */
    @TableField("response_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime responseTime;

    /**
     * 备注
     */
    @TableField("remark")
    private String remark;
}
