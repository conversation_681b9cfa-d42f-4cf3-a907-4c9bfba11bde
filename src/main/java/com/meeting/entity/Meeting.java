package com.meeting.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 会议实体类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting")
public class Meeting extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议标题
     */
    @TableField("title")
    private String title;

    /**
     * 会议描述
     */
    @TableField("description")
    private String description;

    /**
     * 开始时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 会议室ID
     */
    @TableField("room_id")
    private Long roomId;

    /**
     * 组织者ID
     */
    @TableField("organizer_id")
    private Long organizerId;

    /**
     * 会议状态：SCHEDULED-已安排，ONGOING-进行中，COMPLETED-已完成，CANCELLED-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 会议类型：INTERNAL-内部会议，EXTERNAL-外部会议
     */
    @TableField("type")
    private String type;

    /**
     * 会议议程
     */
    @TableField("agenda")
    private String agenda;

    /**
     * 会议资料（JSON格式）
     */
    @TableField("materials")
    private String materials;

    /**
     * 录音/录像URL
     */
    @TableField("recording_url")
    private String recordingUrl;
}
