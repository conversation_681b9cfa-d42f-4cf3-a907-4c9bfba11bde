package com.meeting.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * 会议预约记录实体类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting_booking")
public class MeetingBooking extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议室ID
     */
    @TableField("room_id")
    private Long roomId;

    /**
     * 会议ID（如果有关联会议）
     */
    @TableField("meeting_id")
    private Long meetingId;

    /**
     * 预约用户ID
     */
    @TableField("user_id")
    private Long userId;

    /**
     * 开始时间
     */
    @TableField("start_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @TableField("end_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endTime;

    /**
     * 预约目的
     */
    @TableField("purpose")
    private String purpose;

    /**
     * 预约状态：PENDING-待审核，APPROVED-已通过，REJECTED-已拒绝，CANCELLED-已取消
     */
    @TableField("status")
    private String status;

    /**
     * 审核人ID
     */
    @TableField("approve_user_id")
    private Long approveUserId;

    /**
     * 审核时间
     */
    @TableField("approve_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime approveTime;

    /**
     * 审核备注
     */
    @TableField("approve_remark")
    private String approveRemark;
}
