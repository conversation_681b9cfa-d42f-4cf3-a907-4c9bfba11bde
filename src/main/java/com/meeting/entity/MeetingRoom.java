package com.meeting.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 会议室实体类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("meeting_room")
public class MeetingRoom extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 会议室名称
     */
    @TableField("name")
    private String name;

    /**
     * 会议室位置
     */
    @TableField("location")
    private String location;

    /**
     * 容纳人数
     */
    @TableField("capacity")
    private Integer capacity;

    /**
     * 设备信息（JSON格式）
     */
    @TableField("equipment")
    private String equipment;

    /**
     * 会议室描述
     */
    @TableField("description")
    private String description;

    /**
     * 状态：0-禁用，1-启用
     */
    @TableField("status")
    private Boolean status;
}
