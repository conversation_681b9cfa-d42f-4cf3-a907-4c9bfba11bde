package com.meeting.utils;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Bean拷贝工具类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
public class BeanCopyUtils {

    /**
     * 单个对象拷贝
     *
     * @param source      源对象
     * @param targetClass 目标类
     * @param <T>         目标类型
     * @return 目标对象
     */
    public static <T> T copyBean(Object source, Class<T> targetClass) {
        if (ObjectUtil.isNull(source)) {
            return null;
        }
        return BeanUtil.copyProperties(source, targetClass);
    }

    /**
     * 列表对象拷贝
     *
     * @param sourceList  源列表
     * @param targetClass 目标类
     * @param <T>         目标类型
     * @return 目标列表
     */
    public static <T> List<T> copyBeanList(List<?> sourceList, Class<T> targetClass) {
        if (ObjectUtil.isNull(sourceList) || sourceList.isEmpty()) {
            return List.of();
        }
        return sourceList.stream()
                .map(source -> copyBean(source, targetClass))
                .collect(Collectors.toList());
    }
}