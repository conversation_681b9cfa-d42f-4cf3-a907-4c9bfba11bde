package com.meeting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meeting.entity.MeetingParticipant;
import com.meeting.vo.MeetingParticipantVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 会议参与者Mapper接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Mapper
public interface MeetingParticipantMapper extends BaseMapper<MeetingParticipant> {

    /**
     * 根据会议ID查询参与者列表（包含用户信息）
     *
     * @param meetingId 会议ID
     * @return 参与者列表
     */
    @Select("SELECT p.*, u.username, u.nickname, u.email, d.name as dept_name " +
            "FROM meeting_participant p " +
            "LEFT JOIN sys_user u ON p.user_id = u.id " +
            "LEFT JOIN sys_dept d ON u.dept_id = d.id " +
            "WHERE p.meeting_id = #{meetingId} " +
            "ORDER BY p.role DESC, p.create_time ASC")
    List<MeetingParticipantVO> selectParticipantsByMeetingId(@Param("meetingId") Long meetingId);

    /**
     * 根据用户ID和会议ID查询参与信息
     *
     * @param userId    用户ID
     * @param meetingId 会议ID
     * @return 参与信息
     */
    @Select("SELECT p.*, u.username, u.nickname, u.email, d.name as dept_name " +
            "FROM meeting_participant p " +
            "LEFT JOIN sys_user u ON p.user_id = u.id " +
            "LEFT JOIN sys_dept d ON u.dept_id = d.id " +
            "WHERE p.user_id = #{userId} AND p.meeting_id = #{meetingId}")
    MeetingParticipantVO selectParticipantByUserIdAndMeetingId(@Param("userId") Long userId,
                                                               @Param("meetingId") Long meetingId);
}