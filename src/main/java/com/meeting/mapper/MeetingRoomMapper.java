package com.meeting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meeting.entity.MeetingRoom;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议室Mapper接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Mapper
public interface MeetingRoomMapper extends BaseMapper<MeetingRoom> {

    /**
     * 查询可用的会议室（在指定时间段内没有被预约）
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 可用的会议室列表
     */
    @Select("SELECT r.* FROM meeting_room r " +
            "WHERE r.status = 1 AND r.deleted = 0 " +
            "AND r.id NOT IN (" +
            "  SELECT DISTINCT m.room_id FROM meeting m " +
            "  WHERE m.deleted = 0 " +
            "  AND m.status IN ('SCHEDULED', 'ONGOING') " +
            "  AND ((m.start_time <= #{startTime} AND m.end_time > #{startTime}) " +
            "       OR (m.start_time < #{endTime} AND m.end_time >= #{endTime}) " +
            "       OR (m.start_time >= #{startTime} AND m.end_time <= #{endTime}))" +
            ")")
    List<MeetingRoom> selectAvailableRooms(@Param("startTime") LocalDateTime startTime,
                                           @Param("endTime") LocalDateTime endTime);
}