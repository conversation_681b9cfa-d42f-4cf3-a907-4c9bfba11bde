package com.meeting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.meeting.entity.SysUser;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * 用户Mapper接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Mapper
public interface SysUserMapper extends BaseMapper<SysUser> {

    /**
     * 根据用户名查找用户（包含部门信息）
     *
     * @param username 用户名
     * @return 用户信息
     */
    @Select("SELECT u.*, d.name as dept_name FROM sys_user u " +
            "LEFT JOIN sys_dept d ON u.dept_id = d.id " +
            "WHERE u.username = #{username} AND u.deleted = 0")
    SysUser selectByUsernameWithDept(@Param("username") String username);

    /**
     * 根据ID查找用户（包含部门信息）
     *
     * @param id 用户ID
     * @return 用户信息
     */
    @Select("SELECT u.*, d.name as dept_name FROM sys_user u " +
            "LEFT JOIN sys_dept d ON u.dept_id = d.id " +
            "WHERE u.id = #{id} AND u.deleted = 0")
    SysUser selectByIdWithDept(@Param("id") Long id);
}