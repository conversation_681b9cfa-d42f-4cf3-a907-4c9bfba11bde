package com.meeting.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.meeting.entity.Meeting;
import com.meeting.vo.MeetingVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议Mapp接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Mapper
public interface MeetingMapper extends BaseMapper<Meeting> {

    /**
     * 分页查询会议列表（包含会议室和组织者信息）
     *
     * @param page         分页参数
     * @param title        会议标题
     * @param roomId       会议室ID
     * @param organizerId  组织者ID
     * @param status       会议状态
     * @param type         会议类型
     * @param startTimeFrom 开始时间起始
     * @param startTimeTo   开始时间截止
     * @return 会议列表
     */
    @Select("<script>" +
            "SELECT m.*, r.name as room_name, r.location as room_location, " +
            "       u.nickname as organizer_name " +
            "FROM meeting m " +
            "LEFT JOIN meeting_room r ON m.room_id = r.id " +
            "LEFT JOIN sys_user u ON m.organizer_id = u.id " +
            "WHERE m.deleted = 0 " +
            "<if test='title != null and title != \"\\"\'> AND m.title LIKE CONCAT('%', #{title}, '%') </if>" +
            "<if test='roomId != null'> AND m.room_id = #{roomId} </if>" +
            "<if test='organizerId != null'> AND m.organizer_id = #{organizerId} </if>" +
            "<if test='status != null and status != \"\\"\'> AND m.status = #{status} </if>" +
            "<if test='type != null and type != \"\\"\'> AND m.type = #{type} </if>" +
            "<if test='startTimeFrom != null'> AND m.start_time >= #{startTimeFrom} </if>" +
            "<if test='startTimeTo != null'> AND m.start_time <= #{startTimeTo} </if>" +
            "ORDER BY m.start_time DESC" +
            "</script>")
    Page<MeetingVO> selectMeetingPage(Page<MeetingVO> page,
                                     @Param("title") String title,
                                     @Param("roomId") Long roomId,
                                     @Param("organizerId") Long organizerId,
                                     @Param("status") String status,
                                     @Param("type") String type,
                                     @Param("startTimeFrom") LocalDateTime startTimeFrom,
                                     @Param("startTimeTo") LocalDateTime startTimeTo);

    /**
     * 根据ID查询会议详情（包含会议室和组织者信息）
     *
     * @param id 会议ID
     * @return 会议详情
     */
    @Select("SELECT m.*, r.name as room_name, r.location as room_location, " +
            "       u.nickname as organizer_name " +
            "FROM meeting m " +
            "LEFT JOIN meeting_room r ON m.room_id = r.id " +
            "LEFT JOIN sys_user u ON m.organizer_id = u.id " +
            "WHERE m.id = #{id} AND m.deleted = 0")
    MeetingVO selectMeetingDetailById(@Param("id") Long id);

    /**
     * 查询用户的会议列表（作为参与者或组织者）
     *
     * @param userId 用户ID
     * @return 会议列表
     */
    @Select("SELECT DISTINCT m.*, r.name as room_name, r.location as room_location, " +
            "                u.nickname as organizer_name " +
            "FROM meeting m " +
            "LEFT JOIN meeting_room r ON m.room_id = r.id " +
            "LEFT JOIN sys_user u ON m.organizer_id = u.id " +
            "LEFT JOIN meeting_participant p ON m.id = p.meeting_id " +
            "WHERE m.deleted = 0 " +
            "AND (m.organizer_id = #{userId} OR (p.user_id = #{userId} AND p.status != 'DECLINED')) " +
            "ORDER BY m.start_time DESC")
    List<MeetingVO> selectUserMeetings(@Param("userId") Long userId);
}