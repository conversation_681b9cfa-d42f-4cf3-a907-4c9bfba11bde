package com.meeting.config;

import cn.dev33.satoken.interceptor.SaInterceptor;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

/**
 * Sa-Token 配置
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Configuration
public class SaTokenConfig implements WebMvcConfigurer {

    /**
     * 注册 Sa-Token 拦截器，打开注解式鉴权功能
     */
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 注册 Sa-Token 拦截器，打开注解式鉴权功能
        registry.addInterceptor(new SaInterceptor(handle -> {
            // 指定一堆接口，校验是否登录
            SaRouter.match("/**", r -> StpUtil.checkLogin())
                    // 排除登录、注册、验证码等接口
                    .notMatch("/auth/login")
                    .notMatch("/auth/register")
                    .notMatch("/auth/captcha")
                    // 排除API文档相关接口
                    .notMatch("/doc.html")
                    .notMatch("/v3/api-docs/**")
                    .notMatch("/webjars/**")
                    .notMatch("/swagger-ui/**")
                    .notMatch("/favicon.ico")
                    // 排除Druid监控台
                    .notMatch("/druid/**")
                    // 排除静态资源
                    .notMatch("/static/**")
                    .notMatch("/css/**")
                    .notMatch("/js/**")
                    .notMatch("/images/**");
        })).addPathPatterns("/**");
    }
}