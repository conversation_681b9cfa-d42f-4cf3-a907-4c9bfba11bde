package com.meeting.config;

import io.swagger.v3.oas.models.OpenAPI;
import io.swagger.v3.oas.models.info.Contact;
import io.swagger.v3.oas.models.info.Info;
import io.swagger.v3.oas.models.info.License;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Knife4j 配置
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Configuration
public class Knife4jConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("会议管理系统API文档")
                        .version("1.0.0")
                        .description("会议管理系统后端API接口文档")
                        .contact(new Contact()
                                .name("MiniMax Agent")
                                .email("<EMAIL>")
                                .url("https://meeting.com"))
                        .license(new License()
                                .name("Apache 2.0")
                                .url("https://apache.org/licenses/LICENSE-2.0")));
    }
}
