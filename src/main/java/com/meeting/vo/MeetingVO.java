package com.meeting.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议响应VO
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@Schema(description = "会议信息")
public class MeetingVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会议ID
     */
    @Schema(description = "会议ID")
    private Long id;

    /**
     * 会议标题
     */
    @Schema(description = "会议标题")
    private String title;

    /**
     * 会议描述
     */
    @Schema(description = "会议描述")
    private String description;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    /**
     * 会议室ID
     */
    @Schema(description = "会议室ID")
    private Long roomId;

    /**
     * 会议室名称
     */
    @Schema(description = "会议室名称")
    private String roomName;

    /**
     * 会议室位置
     */
    @Schema(description = "会议室位置")
    private String roomLocation;

    /**
     * 组织者ID
     */
    @Schema(description = "组织者ID")
    private Long organizerId;

    /**
     * 组织者名称
     */
    @Schema(description = "组织者名称")
    private String organizerName;

    /**
     * 会议状态
     */
    @Schema(description = "会议状态")
    private String status;

    /**
     * 会议状态描述
     */
    @Schema(description = "会议状态描述")
    private String statusDesc;

    /**
     * 会议类型
     */
    @Schema(description = "会议类型")
    private String type;

    /**
     * 会议议程
     */
    @Schema(description = "会议议程")
    private String agenda;

    /**
     * 会议资料
     */
    @Schema(description = "会议资料")
    private String materials;

    /**
     * 录音/录像URL
     */
    @Schema(description = "录音/录像URL")
    private String recordingUrl;

    /**
     * 参会人员列表
     */
    @Schema(description = "参会人员列表")
    private List<MeetingParticipantVO> participants;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}