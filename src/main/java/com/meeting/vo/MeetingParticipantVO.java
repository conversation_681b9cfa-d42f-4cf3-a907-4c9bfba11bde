package com.meeting.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会议参与者响应VO
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@Schema(description = "会议参与者信息")
public class MeetingParticipantVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Schema(description = "ID")
    private Long id;

    /**
     * 会议ID
     */
    @Schema(description = "会议ID")
    private Long meetingId;

    /**
     * 用户ID
     */
    @Schema(description = "用户ID")
    private Long userId;

    /**
     * 用户名
     */
    @Schema(description = "用户名")
    private String username;

    /**
     * 昵称
     */
    @Schema(description = "昵称")
    private String nickname;

    /**
     * 邮箱
     */
    @Schema(description = "邮箱")
    private String email;

    /**
     * 部门名称
     */
    @Schema(description = "部门名称")
    private String deptName;

    /**
     * 参会角色
     */
    @Schema(description = "参会角色")
    private String role;

    /**
     * 参会状态
     */
    @Schema(description = "参会状态")
    private String status;

    /**
     * 参会状态描述
     */
    @Schema(description = "参会状态描述")
    private String statusDesc;

    /**
     * 邀请时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "邀请时间")
    private LocalDateTime invitedTime;

    /**
     * 响应时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "响应时间")
    private LocalDateTime responseTime;

    /**
     * 备注
     */
    @Schema(description = "备注")
    private String remark;
}