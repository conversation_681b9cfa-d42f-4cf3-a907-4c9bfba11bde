package com.meeting.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会议室响应VO
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@Schema(description = "会议室信息")
public class MeetingRoomVO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会议室ID
     */
    @Schema(description = "会议室ID")
    private Long id;

    /**
     * 会议室名称
     */
    @Schema(description = "会议室名称")
    private String name;

    /**
     * 会议室位置
     */
    @Schema(description = "会议室位置")
    private String location;

    /**
     * 容纳人数
     */
    @Schema(description = "容纳人数")
    private Integer capacity;

    /**
     * 设备信息
     */
    @Schema(description = "设备信息")
    private String equipment;

    /**
     * 会议室描述
     */
    @Schema(description = "会议室描述")
    private String description;

    /**
     * 状态
     */
    @Schema(description = "状态")
    private Boolean status;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "创建时间")
    private LocalDateTime createTime;
}