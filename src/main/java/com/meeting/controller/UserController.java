package com.meeting.controller;

import com.meeting.common.Result;
import com.meeting.service.SysUserService;
import com.meeting.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 用户控制器
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/users")
@RequiredArgsConstructor
@Tag(name = "用户管理", description = "用户信息查询相关接口")
public class UserController {

    private final SysUserService sysUserService;

    @GetMapping("/list")
    @Operation(summary = "获取所有用户列表", description = "获取系统中所有用户的列表")
    public Result<List<UserVO>> getAllUsers() {
        List<UserVO> users = sysUserService.getAllUsers();
        return Result.success(users);
    }

    @PostMapping("/batch")
    @Operation(summary = "批量获取用户信息", description = "根据用户ID列表批量获取用户信息")
    public Result<List<UserVO>> getUsersByIds(
            @Parameter(description = "用户ID列表", required = true)
            @RequestBody List<Long> userIds) {
        List<UserVO> users = sysUserService.getUsersByIds(userIds);
        return Result.success(users);
    }
}