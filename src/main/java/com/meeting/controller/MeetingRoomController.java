package com.meeting.controller;

import com.meeting.common.Result;
import com.meeting.service.MeetingRoomService;
import com.meeting.vo.MeetingRoomVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议室控制器
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/rooms")
@RequiredArgsConstructor
@Tag(name = "会议室管理", description = "会议室查询、可用性检查等相关接口")
public class MeetingRoomController {

    private final MeetingRoomService meetingRoomService;

    @GetMapping("/list")
    @Operation(summary = "获取所有会议室列表", description = "获取系统中所有可用的会议室列表")
    public Result<List<MeetingRoomVO>> getAllRooms() {
        List<MeetingRoomVO> rooms = meetingRoomService.getAllRooms();
        return Result.success(rooms);
    }

    @GetMapping("/available")
    @Operation(summary = "获取可用会议室列表", description = "根据时间段获取可用的会议室列表")
    public Result<List<MeetingRoomVO>> getAvailableRooms(
            @Parameter(description = "开始时间", required = true, example = "2025-08-01 09:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime startTime,
            @Parameter(description = "结束时间", required = true, example = "2025-08-01 11:00:00")
            @RequestParam @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss") LocalDateTime endTime) {
        List<MeetingRoomVO> rooms = meetingRoomService.getAvailableRooms(startTime, endTime);
        return Result.success(rooms);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取会议室详情", description = "获取指定会议室的详细信息")
    public Result<MeetingRoomVO> getRoomDetail(
            @Parameter(description = "会议室ID", required = true) @PathVariable Long id) {
        MeetingRoomVO room = meetingRoomService.getRoomDetail(id);
        return Result.success(room);
    }
}