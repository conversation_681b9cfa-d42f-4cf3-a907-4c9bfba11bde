package com.meeting.controller;

import cn.dev33.satoken.stp.StpUtil;
import com.meeting.common.PageResult;
import com.meeting.common.Result;
import com.meeting.dto.MeetingDTO;
import com.meeting.dto.MeetingQueryDTO;
import com.meeting.service.MeetingParticipantService;
import com.meeting.service.MeetingService;
import com.meeting.vo.MeetingParticipantVO;
import com.meeting.vo.MeetingVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 会议控制器
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/meetings")
@RequiredArgsConstructor
@Tag(name = "会议管理", description = "会议创建、查询、修改、删除等相关接口")
public class MeetingController {

    private final MeetingService meetingService;
    private final MeetingParticipantService meetingParticipantService;

    @PostMapping
    @Operation(summary = "创建会议", description = "创建新的会议")
    public Result<Boolean> createMeeting(@Validated @RequestBody MeetingDTO meetingDTO) {
        Boolean result = meetingService.createMeeting(meetingDTO);
        return Result.success("会议创建成功", result);
    }

    @PutMapping("/{id}")
    @Operation(summary = "更新会议", description = "更新会议信息")
    public Result<Boolean> updateMeeting(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id,
            @Validated @RequestBody MeetingDTO meetingDTO) {
        meetingDTO.setId(id);
        Boolean result = meetingService.updateMeeting(meetingDTO);
        return Result.success("会议更新成功", result);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "删除会议", description = "删除指定的会议")
    public Result<Boolean> deleteMeeting(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id) {
        Boolean result = meetingService.deleteMeeting(id);
        return Result.success("会议删除成功", result);
    }

    @GetMapping("/page")
    @Operation(summary = "分页查询会议列表", description = "根据条件分页查询会议列表")
    public Result<PageResult<MeetingVO>> getMeetingPage(MeetingQueryDTO queryDTO) {
        PageResult<MeetingVO> result = meetingService.getMeetingPage(queryDTO);
        return Result.success(result);
    }

    @GetMapping("/{id}")
    @Operation(summary = "获取会议详情", description = "获取指定会议的详细信息")
    public Result<MeetingVO> getMeetingDetail(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id) {
        MeetingVO meetingVO = meetingService.getMeetingDetail(id);
        return Result.success(meetingVO);
    }

    @GetMapping("/my")
    @Operation(summary = "获取我的会议列表", description = "获取当前用户的会议列表（作为组织者或参与者）")
    public Result<List<MeetingVO>> getMyMeetings() {
        List<MeetingVO> meetings = meetingService.getUserMeetings(null);
        return Result.success(meetings);
    }

    @PostMapping("/{id}/cancel")
    @Operation(summary = "取消会议", description = "取消指定的会议")
    public Result<Boolean> cancelMeeting(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id) {
        Boolean result = meetingService.cancelMeeting(id);
        return Result.success("会议取消成功", result);
    }

    @PostMapping("/{id}/start")
    @Operation(summary = "开始会议", description = "开始指定的会议")
    public Result<Boolean> startMeeting(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id) {
        Boolean result = meetingService.startMeeting(id);
        return Result.success("会议开始成功", result);
    }

    @PostMapping("/{id}/end")
    @Operation(summary = "结束会议", description = "结束指定的会议")
    public Result<Boolean> endMeeting(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id) {
        Boolean result = meetingService.endMeeting(id);
        return Result.success("会议结束成功", result);
    }

    @GetMapping("/{id}/participants")
    @Operation(summary = "获取会议参与者列表", description = "获取指定会议的参与者列表")
    public Result<List<MeetingParticipantVO>> getMeetingParticipants(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id) {
        List<MeetingParticipantVO> participants = meetingParticipantService.getParticipantsByMeetingId(id);
        return Result.success(participants);
    }

    @PostMapping("/{id}/participants/add")
    @Operation(summary = "添加会议参与者", description = "向指定会议添加参与者")
    public Result<Boolean> addParticipants(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id,
            @Parameter(description = "参与者ID列表", required = true) @RequestBody List<Long> participantIds) {
        Boolean result = meetingParticipantService.addParticipants(id, participantIds);
        return Result.success("参与者添加成功", result);
    }

    @PostMapping("/{id}/participants/remove")
    @Operation(summary = "移除会议参与者", description = "从指定会议中移除参与者")
    public Result<Boolean> removeParticipants(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id,
            @Parameter(description = "参与者ID列表", required = true) @RequestBody List<Long> participantIds) {
        Boolean result = meetingParticipantService.removeParticipants(id, participantIds);
        return Result.success("参与者移除成功", result);
    }

    @PostMapping("/{id}/participants/status")
    @Operation(summary = "更新参会状态", description = "更新当前用户在指定会议中的参会状态")
    public Result<Boolean> updateParticipantStatus(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id,
            @Parameter(description = "参会状态", required = true) @RequestParam String status) {
        Long userId = StpUtil.getLoginIdAsLong();
        Boolean result = meetingParticipantService.updateParticipantStatus(id, userId, status);
        return Result.success("参会状态更新成功", result);
    }

    @GetMapping("/{id}/participants/my-status")
    @Operation(summary = "获取我的参会状态", description = "获取当前用户在指定会议中的参会状态")
    public Result<MeetingParticipantVO> getMyParticipantStatus(
            @Parameter(description = "会议ID", required = true) @PathVariable Long id) {
        Long userId = StpUtil.getLoginIdAsLong();
        MeetingParticipantVO participant = meetingParticipantService.getParticipantStatus(id, userId);
        return Result.success(participant);
    }
}