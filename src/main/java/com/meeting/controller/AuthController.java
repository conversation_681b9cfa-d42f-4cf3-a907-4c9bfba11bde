package com.meeting.controller;

import com.meeting.common.Result;
import com.meeting.dto.LoginDTO;
import com.meeting.dto.RegisterDTO;
import com.meeting.service.SysUserService;
import com.meeting.vo.LoginVO;
import com.meeting.vo.UserVO;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

/**
 * 认证控制器
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
@Tag(name = "认证管理", description = "用户登录、注册、退出等认证相关接口")
public class AuthController {

    private final SysUserService sysUserService;

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户登录接口")
    public Result<LoginVO> login(@Validated @RequestBody LoginDTO loginDTO) {
        LoginVO loginVO = sysUserService.login(loginDTO);
        return Result.success("登录成功", loginVO);
    }

    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "用户注册接口")
    public Result<Boolean> register(@Validated @RequestBody RegisterDTO registerDTO) {
        Boolean result = sysUserService.register(registerDTO);
        return Result.success("注册成功", result);
    }

    @PostMapping("/logout")
    @Operation(summary = "用户退出", description = "用户退出登录接口")
    public Result<Void> logout() {
        sysUserService.logout();
        return Result.success("退出成功",null);
    }

    @GetMapping("/current")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public Result<UserVO> getCurrentUser() {
        UserVO userVO = sysUserService.getCurrentUser();
        return Result.success(userVO);
    }
}