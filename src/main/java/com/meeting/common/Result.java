package com.meeting.common;

import lombok.Data;

import java.io.Serializable;

/**
 * 统一响应结果
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
public class Result<T> implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 状态码
     */
    private Integer code;

    /**
     * 响应消息
     */
    private String message;

    /**
     * 响应数据
     */
    private T data;

    /**
     * 成功标志
     */
    private Boolean success;

    /**
     * 时间戳
     */
    private Long timestamp;

    public Result() {
        this.timestamp = System.currentTimeMillis();
    }

    /**
     * 成功响应
     */
    public static <T> Result<T> success() {
        Result<T> result = new Result<>();
        result.setCode(200);
        result.setMessage("操作成功");
        result.setSuccess(true);
        return result;
    }

    /**
     * 成功响应，携带数据
     */
    public static <T> Result<T> success(T data) {
        Result<T> result = success();
        result.setData(data);
        return result;
    }

    /**
     * 成功响应，携带消息和数据
     */
    public static <T> Result<T> success(String message, T data) {
        Result<T> result = success(data);
        result.setMessage(message);
        return result;
    }

    /**
     * 失败响应
     */
    public static <T> Result<T> error() {
        Result<T> result = new Result<>();
        result.setCode(500);
        result.setMessage("操作失败");
        result.setSuccess(false);
        return result;
    }

    /**
     * 失败响应，携带消息
     */
    public static <T> Result<T> error(String message) {
        Result<T> result = error();
        result.setMessage(message);
        return result;
    }

    /**
     * 失败响应，携带状态码和消息
     */
    public static <T> Result<T> error(Integer code, String message) {
        Result<T> result = error(message);
        result.setCode(code);
        return result;
    }

    /**
     * 自定义响应
     */
    public static <T> Result<T> result(Boolean success, Integer code, String message, T data) {
        Result<T> result = new Result<>();
        result.setSuccess(success);
        result.setCode(code);
        result.setMessage(message);
        result.setData(data);
        return result;
    }
}
