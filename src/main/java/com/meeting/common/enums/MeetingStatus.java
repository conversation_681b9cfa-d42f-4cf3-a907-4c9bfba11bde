package com.meeting.common.enums;

/**
 * 会议状态枚举
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
public enum MeetingStatus {
    
    SCHEDULED("SCHEDULED", "已安排"),
    ONGOING("ONGOING", "进行中"),
    COMPLETED("COMPLETED", "已完成"),
    CANCELLED("CANCELLED", "已取消");

    private final String code;
    private final String desc;

    MeetingStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static MeetingStatus getByCode(String code) {
        for (MeetingStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}