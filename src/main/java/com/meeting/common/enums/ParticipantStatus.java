package com.meeting.common.enums;

/**
 * 参会状态枚举
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
public enum ParticipantStatus {
    
    PENDING("PENDING", "待确认"),
    ACCEPTED("ACCEPTED", "已接受"),
    DECLINED("DECLINED", "已拒绝"),
    ATTENDED("ATTENDED", "已参加");

    private final String code;
    private final String desc;

    ParticipantStatus(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static ParticipantStatus getByCode(String code) {
        for (ParticipantStatus status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        return null;
    }
}