package com.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meeting.common.PageResult;
import com.meeting.dto.MeetingDTO;
import com.meeting.dto.MeetingQueryDTO;
import com.meeting.entity.Meeting;
import com.meeting.vo.MeetingVO;

import java.util.List;

/**
 * 会议服务接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
public interface MeetingService extends IService<Meeting> {

    /**
     * 创建会议
     *
     * @param meetingDTO 会议信息
     * @return 是否创建成功
     */
    Boolean createMeeting(MeetingDTO meetingDTO);

    /**
     * 更新会议
     *
     * @param meetingDTO 会议信息
     * @return 是否更新成功
     */
    Boolean updateMeeting(MeetingDTO meetingDTO);

    /**
     * 删除会议
     *
     * @param id 会议ID
     * @return 是否删除成功
     */
    Boolean deleteMeeting(Long id);

    /**
     * 分页查询会议列表
     *
     * @param queryDTO 查询条件
     * @return 会议列表
     */
    PageResult<MeetingVO> getMeetingPage(MeetingQueryDTO queryDTO);

    /**
     * 获取会议详情
     *
     * @param id 会议ID
     * @return 会议详情
     */
    MeetingVO getMeetingDetail(Long id);

    /**
     * 获取用户的会议列表
     *
     * @param userId 用户ID
     * @return 会议列表
     */
    List<MeetingVO> getUserMeetings(Long userId);

    /**
     * 取消会议
     *
     * @param id 会议ID
     * @return 是否取消成功
     */
    Boolean cancelMeeting(Long id);

    /**
     * 开始会议
     *
     * @param id 会议ID
     * @return 是否开始成功
     */
    Boolean startMeeting(Long id);

    /**
     * 结束会议
     *
     * @param id 会议ID
     * @return 是否结束成功
     */
    Boolean endMeeting(Long id);
}