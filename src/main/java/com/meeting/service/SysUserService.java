package com.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meeting.dto.LoginDTO;
import com.meeting.dto.RegisterDTO;
import com.meeting.entity.SysUser;
import com.meeting.vo.LoginVO;
import com.meeting.vo.UserVO;

import java.util.List;

/**
 * 用户服务接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
public interface SysUserService extends IService<SysUser> {

    /**
     * 用户登录
     *
     * @param loginDTO 登录参数
     * @return 登录结果
     */
    LoginVO login(LoginDTO loginDTO);

    /**
     * 用户注册
     *
     * @param registerDTO 注册参数
     * @return 注册结果
     */
    Boolean register(RegisterDTO registerDTO);

    /**
     * 用户退出登录
     */
    void logout();

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    UserVO getCurrentUser();

    /**
     * 根据用户名查找用户
     *
     * @param username 用户名
     * @return 用户信息
     */
    SysUser getUserByUsername(String username);

    /**
     * 根据ID列表查询用户列表
     *
     * @param userIds 用户ID列表
     * @return 用户列表
     */
    List<UserVO> getUsersByIds(List<Long> userIds);

    /**
     * 查询所有用户
     *
     * @return 用户列表
     */
    List<UserVO> getAllUsers();
}