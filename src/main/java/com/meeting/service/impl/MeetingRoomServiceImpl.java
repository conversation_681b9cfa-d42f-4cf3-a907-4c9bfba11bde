package com.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meeting.entity.MeetingRoom;
import com.meeting.exception.BusinessException;
import com.meeting.mapper.MeetingRoomMapper;
import com.meeting.service.MeetingRoomService;
import com.meeting.utils.BeanCopyUtils;
import com.meeting.vo.MeetingRoomVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议室服务实现类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingRoomServiceImpl extends ServiceImpl<MeetingRoomMapper, MeetingRoom> implements MeetingRoomService {

    @Override
    public List<MeetingRoomVO> getAllRooms() {
        LambdaQueryWrapper<MeetingRoom> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MeetingRoom::getStatus, true)
                .orderByAsc(MeetingRoom::getName);
        
        List<MeetingRoom> rooms = baseMapper.selectList(wrapper);
        return BeanCopyUtils.copyBeanList(rooms, MeetingRoomVO.class);
    }

    @Override
    public List<MeetingRoomVO> getAvailableRooms(LocalDateTime startTime, LocalDateTime endTime) {
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            throw new BusinessException("开始时间和结束时间不能为空");
        }
        
        if (startTime.isAfter(endTime)) {
            throw new BusinessException("开始时间不能晚于结束时间");
        }
        
        List<MeetingRoom> rooms = baseMapper.selectAvailableRooms(startTime, endTime);
        return BeanCopyUtils.copyBeanList(rooms, MeetingRoomVO.class);
    }

    @Override
    public MeetingRoomVO getRoomDetail(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new BusinessException("会议室ID不能为空");
        }
        
        MeetingRoom room = baseMapper.selectById(id);
        if (ObjectUtil.isNull(room)) {
            throw new BusinessException("会议室不存在");
        }
        
        return BeanCopyUtils.copyBean(room, MeetingRoomVO.class);
    }
}