package com.meeting.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meeting.common.enums.UserRole;
import com.meeting.dto.LoginDTO;
import com.meeting.dto.RegisterDTO;
import com.meeting.entity.SysDept;
import com.meeting.entity.SysUser;
import com.meeting.exception.BusinessException;
import com.meeting.mapper.SysDeptMapper;
import com.meeting.mapper.SysUserMapper;
import com.meeting.service.SysUserService;
import com.meeting.utils.BeanCopyUtils;
import com.meeting.utils.PasswordUtils;
import com.meeting.vo.LoginVO;
import com.meeting.vo.UserVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户服务实现类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class SysUserServiceImpl extends ServiceImpl<SysUserMapper, SysUser> implements SysUserService {

    private final SysDeptMapper sysDeptMapper;

    @Override
    public LoginVO login(LoginDTO loginDTO) {
        // 检查用户是否存在
        SysUser user = baseMapper.selectByUsernameWithDept(loginDTO.getUsername());
        if (ObjectUtil.isNull(user)) {
            throw new BusinessException("用户名或密码错误");
        }

        // 检查用户状态
        if (!user.getStatus()) {
            throw new BusinessException("用户已被禁用，请联系管理员");
        }

        // 验证密码
        if (!PasswordUtils.matches(loginDTO.getPassword(), user.getPassword())) {
            throw new BusinessException("用户名或密码错误");
        }

        // 登录成功，生成token
        StpUtil.login(user.getId());
        String token = StpUtil.getTokenValue();

        // 构造返回结果
        LoginVO loginVO = BeanCopyUtils.copyBean(user, LoginVO.class);
        loginVO.setToken(token);
        loginVO.setUserId(user.getId());
        loginVO.setLoginTime(LocalDateTime.now());
        loginVO.setExpireTime(LocalDateTime.now().plusSeconds(StpUtil.getTokenTimeout()));
        
        // 获取部门名称
        if (ObjectUtil.isNotNull(user.getDeptId())) {
            SysDept dept = sysDeptMapper.selectById(user.getDeptId());
            if (ObjectUtil.isNotNull(dept)) {
                loginVO.setDeptName(dept.getName());
            }
        }

        log.info("用户登录成功：{}", user.getUsername());
        return loginVO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean register(RegisterDTO registerDTO) {
        // 检查用户名是否已存在
        SysUser existUser = getUserByUsername(registerDTO.getUsername());
        if (ObjectUtil.isNotNull(existUser)) {
            throw new BusinessException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (StrUtil.isNotBlank(registerDTO.getEmail())) {
            LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(SysUser::getEmail, registerDTO.getEmail());
            SysUser emailUser = baseMapper.selectOne(wrapper);
            if (ObjectUtil.isNotNull(emailUser)) {
                throw new BusinessException("邮箱已被使用");
            }
        }

        // 检查密码是否一致
        if (!registerDTO.getPassword().equals(registerDTO.getConfirmPassword())) {
            throw new BusinessException("两次输入的密码不一致");
        }

        // 创建用户
        SysUser user = BeanCopyUtils.copyBean(registerDTO, SysUser.class);
        user.setPassword(PasswordUtils.encode(registerDTO.getPassword()));
        user.setStatus(true);
        user.setRole(UserRole.USER.getCode());

        boolean result = save(user);
        if (result) {
            log.info("用户注册成功：{}", user.getUsername());
        }
        return result;
    }

    @Override
    public void logout() {
        StpUtil.logout();
        log.info("用户退出登录成功");
    }

    @Override
    public UserVO getCurrentUser() {
        Long userId = StpUtil.getLoginIdAsLong();
        SysUser user = baseMapper.selectByIdWithDept(userId);
        if (ObjectUtil.isNull(user)) {
            throw new BusinessException("用户不存在");
        }

        UserVO userVO = BeanCopyUtils.copyBean(user, UserVO.class);
        
        // 获取部门名称
        if (ObjectUtil.isNotNull(user.getDeptId())) {
            SysDept dept = sysDeptMapper.selectById(user.getDeptId());
            if (ObjectUtil.isNotNull(dept)) {
                userVO.setDeptName(dept.getName());
            }
        }
        
        return userVO;
    }

    @Override
    public SysUser getUserByUsername(String username) {
        if (StrUtil.isBlank(username)) {
            return null;
        }
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getUsername, username);
        return baseMapper.selectOne(wrapper);
    }

    @Override
    public List<UserVO> getUsersByIds(List<Long> userIds) {
        if (ObjectUtil.isEmpty(userIds)) {
            return List.of();
        }
        
        List<SysUser> users = baseMapper.selectBatchIds(userIds);
        List<UserVO> userVOs = BeanCopyUtils.copyBeanList(users, UserVO.class);
        
        // 填充部门信息
        userVOs.forEach(userVO -> {
            if (ObjectUtil.isNotNull(userVO.getDeptId())) {
                SysDept dept = sysDeptMapper.selectById(userVO.getDeptId());
                if (ObjectUtil.isNotNull(dept)) {
                    userVO.setDeptName(dept.getName());
                }
            }
        });
        
        return userVOs;
    }

    @Override
    public List<UserVO> getAllUsers() {
        LambdaQueryWrapper<SysUser> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysUser::getStatus, true)
                .orderByAsc(SysUser::getDeptId)
                .orderByAsc(SysUser::getCreateTime);
        
        List<SysUser> users = baseMapper.selectList(wrapper);
        List<UserVO> userVOs = BeanCopyUtils.copyBeanList(users, UserVO.class);
        
        // 填充部门信息
        userVOs.forEach(userVO -> {
            if (ObjectUtil.isNotNull(userVO.getDeptId())) {
                SysDept dept = sysDeptMapper.selectById(userVO.getDeptId());
                if (ObjectUtil.isNotNull(dept)) {
                    userVO.setDeptName(dept.getName());
                }
            }
        });
        
        return userVOs;
    }
}