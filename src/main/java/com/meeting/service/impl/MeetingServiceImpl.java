package com.meeting.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meeting.common.PageResult;
import com.meeting.common.enums.MeetingStatus;
import com.meeting.common.enums.ParticipantStatus;
import com.meeting.dto.MeetingDTO;
import com.meeting.dto.MeetingQueryDTO;
import com.meeting.entity.Meeting;
import com.meeting.entity.MeetingParticipant;
import com.meeting.entity.MeetingRoom;
import com.meeting.exception.BusinessException;
import com.meeting.mapper.MeetingMapper;
import com.meeting.service.MeetingParticipantService;
import com.meeting.service.MeetingRoomService;
import com.meeting.service.MeetingService;
import com.meeting.utils.BeanCopyUtils;
import com.meeting.vo.MeetingParticipantVO;
import com.meeting.vo.MeetingVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议服务实现类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingServiceImpl extends ServiceImpl<MeetingMapper, Meeting> implements MeetingService {

    private final MeetingRoomService meetingRoomService;
    private final MeetingParticipantService meetingParticipantService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean createMeeting(MeetingDTO meetingDTO) {
        // 参数验证
        validateMeetingTime(meetingDTO.getStartTime(), meetingDTO.getEndTime());
        
        // 检查会议室是否可用
        checkRoomAvailability(meetingDTO.getRoomId(), meetingDTO.getStartTime(), meetingDTO.getEndTime(), null);
        
        // 创建会议
        Meeting meeting = BeanCopyUtils.copyBean(meetingDTO, Meeting.class);
        meeting.setOrganizerId(StpUtil.getLoginIdAsLong());
        meeting.setStatus(MeetingStatus.SCHEDULED.getCode());
        
        boolean result = save(meeting);
        
        if (result) {
            // 添加组织者为参与者
            MeetingParticipant organizer = new MeetingParticipant();
            organizer.setMeetingId(meeting.getId());
            organizer.setUserId(meeting.getOrganizerId());
            organizer.setRole("ORGANIZER");
            organizer.setStatus(ParticipantStatus.ACCEPTED.getCode());
            organizer.setInvitedTime(LocalDateTime.now());
            organizer.setResponseTime(LocalDateTime.now());
            meetingParticipantService.save(organizer);
            
            // 添加其他参与者
            if (ObjectUtil.isNotEmpty(meetingDTO.getParticipantIds())) {
                meetingParticipantService.addParticipants(meeting.getId(), meetingDTO.getParticipantIds());
            }
            
            log.info("创建会议成功：{}", meeting.getTitle());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateMeeting(MeetingDTO meetingDTO) {
        if (ObjectUtil.isNull(meetingDTO.getId())) {
            throw new BusinessException("会议ID不能为空");
        }
        
        Meeting existMeeting = getById(meetingDTO.getId());
        if (ObjectUtil.isNull(existMeeting)) {
            throw new BusinessException("会议不存在");
        }
        
        // 检查权限
        Long currentUserId = StpUtil.getLoginIdAsLong();
        if (!existMeeting.getOrganizerId().equals(currentUserId)) {
            throw new BusinessException("只有会议组织者才能修改会议");
        }
        
        // 检查会议状态
        if (!MeetingStatus.SCHEDULED.getCode().equals(existMeeting.getStatus())) {
            throw new BusinessException("只能修改已安排状态的会议");
        }
        
        // 验证时间
        validateMeetingTime(meetingDTO.getStartTime(), meetingDTO.getEndTime());
        
        // 如果会议室或时间发生变化，检查会议室可用性
        if (!existMeeting.getRoomId().equals(meetingDTO.getRoomId()) ||
            !existMeeting.getStartTime().equals(meetingDTO.getStartTime()) ||
            !existMeeting.getEndTime().equals(meetingDTO.getEndTime())) {
            checkRoomAvailability(meetingDTO.getRoomId(), meetingDTO.getStartTime(), 
                                meetingDTO.getEndTime(), meetingDTO.getId());
        }
        
        // 更新会议信息
        Meeting meeting = BeanCopyUtils.copyBean(meetingDTO, Meeting.class);
        meeting.setOrganizerId(existMeeting.getOrganizerId());
        meeting.setStatus(existMeeting.getStatus());
        
        boolean result = updateById(meeting);
        
        if (result) {
            // 更新参与者
            if (ObjectUtil.isNotEmpty(meetingDTO.getParticipantIds())) {
                // 移除原有参与者（除组织者外）
                LambdaQueryWrapper<MeetingParticipant> wrapper = new LambdaQueryWrapper<>();
                wrapper.eq(MeetingParticipant::getMeetingId, meeting.getId())
                        .ne(MeetingParticipant::getRole, "ORGANIZER");
                meetingParticipantService.remove(wrapper);
                
                // 添加新的参与者
                meetingParticipantService.addParticipants(meeting.getId(), meetingDTO.getParticipantIds());
            }
            
            log.info("更新会议成功：{}", meeting.getTitle());
        }
        
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteMeeting(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new BusinessException("会议ID不能为空");
        }
        
        Meeting meeting = getById(id);
        if (ObjectUtil.isNull(meeting)) {
            throw new BusinessException("会议不存在");
        }
        
        // 检查权限
        Long currentUserId = StpUtil.getLoginIdAsLong();
        if (!meeting.getOrganizerId().equals(currentUserId)) {
            throw new BusinessException("只有会议组织者才能删除会议");
        }
        
        // 检查会议状态
        if (MeetingStatus.ONGOING.getCode().equals(meeting.getStatus())) {
            throw new BusinessException("进行中的会议不能删除");
        }
        
        boolean result = removeById(id);
        
        if (result) {
            // 删除相关的参与者记录
            LambdaQueryWrapper<MeetingParticipant> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MeetingParticipant::getMeetingId, id);
            meetingParticipantService.remove(wrapper);
            
            log.info("删除会议成功：{}", meeting.getTitle());
        }
        
        return result;
    }

    @Override
    public PageResult<MeetingVO> getMeetingPage(MeetingQueryDTO queryDTO) {
        Page<MeetingVO> page = new Page<>(queryDTO.getCurrent(), queryDTO.getSize());
        Page<MeetingVO> resultPage = baseMapper.selectMeetingPage(page,
                queryDTO.getTitle(),
                queryDTO.getRoomId(),
                queryDTO.getOrganizerId(),
                queryDTO.getStatus(),
                queryDTO.getType(),
                queryDTO.getStartTimeFrom(),
                queryDTO.getStartTimeTo());
        
        // 设置状态描述
        resultPage.getRecords().forEach(meetingVO -> {
            MeetingStatus status = MeetingStatus.getByCode(meetingVO.getStatus());
            if (ObjectUtil.isNotNull(status)) {
                meetingVO.setStatusDesc(status.getDesc());
            }
        });
        
        return new PageResult<>(resultPage.getRecords(), resultPage.getTotal(),
                resultPage.getCurrent(), resultPage.getSize());
    }

    @Override
    public MeetingVO getMeetingDetail(Long id) {
        if (ObjectUtil.isNull(id)) {
            throw new BusinessException("会议ID不能为空");
        }
        
        MeetingVO meetingVO = baseMapper.selectMeetingDetailById(id);
        if (ObjectUtil.isNull(meetingVO)) {
            throw new BusinessException("会议不存在");
        }
        
        // 设置状态描述
        MeetingStatus status = MeetingStatus.getByCode(meetingVO.getStatus());
        if (ObjectUtil.isNotNull(status)) {
            meetingVO.setStatusDesc(status.getDesc());
        }
        
        // 获取参与者列表
        List<MeetingParticipantVO> participants = meetingParticipantService.getParticipantsByMeetingId(id);
        meetingVO.setParticipants(participants);
        
        return meetingVO;
    }

    @Override
    public List<MeetingVO> getUserMeetings(Long userId) {
        if (ObjectUtil.isNull(userId)) {
            userId = StpUtil.getLoginIdAsLong();
        }
        
        List<MeetingVO> meetings = baseMapper.selectUserMeetings(userId);
        
        // 设置状态描述
        meetings.forEach(meetingVO -> {
            MeetingStatus status = MeetingStatus.getByCode(meetingVO.getStatus());
            if (ObjectUtil.isNotNull(status)) {
                meetingVO.setStatusDesc(status.getDesc());
            }
        });
        
        return meetings;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean cancelMeeting(Long id) {
        return updateMeetingStatus(id, MeetingStatus.CANCELLED.getCode(), "取消");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean startMeeting(Long id) {
        return updateMeetingStatus(id, MeetingStatus.ONGOING.getCode(), "开始");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean endMeeting(Long id) {
        return updateMeetingStatus(id, MeetingStatus.COMPLETED.getCode(), "结束");
    }

    /**
     * 更新会议状态
     */
    private Boolean updateMeetingStatus(Long id, String newStatus, String operation) {
        if (ObjectUtil.isNull(id)) {
            throw new BusinessException("会议ID不能为空");
        }
        
        Meeting meeting = getById(id);
        if (ObjectUtil.isNull(meeting)) {
            throw new BusinessException("会议不存在");
        }
        
        // 检查权限
        Long currentUserId = StpUtil.getLoginIdAsLong();
        if (!meeting.getOrganizerId().equals(currentUserId)) {
            throw new BusinessException("只有会议组织者才能" + operation + "会议");
        }
        
        meeting.setStatus(newStatus);
        boolean result = updateById(meeting);
        
        if (result) {
            log.info("{}会议成功：{}", operation, meeting.getTitle());
        }
        
        return result;
    }

    /**
     * 验证会议时间
     */
    private void validateMeetingTime(LocalDateTime startTime, LocalDateTime endTime) {
        if (ObjectUtil.isNull(startTime) || ObjectUtil.isNull(endTime)) {
            throw new BusinessException("会议开始时间和结束时间不能为空");
        }
        
        if (startTime.isBefore(LocalDateTime.now())) {
            throw new BusinessException("会议开始时间不能早于当前时间");
        }
        
        if (startTime.isAfter(endTime)) {
            throw new BusinessException("会议开始时间不能晚于结束时间");
        }
        
        if (startTime.isEqual(endTime)) {
            throw new BusinessException("会议开始时间和结束时间不能相同");
        }
    }

    /**
     * 检查会议室可用性
     */
    private void checkRoomAvailability(Long roomId, LocalDateTime startTime, LocalDateTime endTime, Long excludeMeetingId) {
        // 检查会议室是否存在
        MeetingRoom room = meetingRoomService.getById(roomId);
        if (ObjectUtil.isNull(room)) {
            throw new BusinessException("会议室不存在");
        }
        
        if (!room.getStatus()) {
            throw new BusinessException("会议室已禁用");
        }
        
        // 检查时间冲突
        LambdaQueryWrapper<Meeting> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(Meeting::getRoomId, roomId)
                .in(Meeting::getStatus, MeetingStatus.SCHEDULED.getCode(), MeetingStatus.ONGOING.getCode())
                .and(w -> w
                    .and(w1 -> w1.le(Meeting::getStartTime, startTime).gt(Meeting::getEndTime, startTime))
                    .or(w2 -> w2.lt(Meeting::getStartTime, endTime).ge(Meeting::getEndTime, endTime))
                    .or(w3 -> w3.ge(Meeting::getStartTime, startTime).le(Meeting::getEndTime, endTime))
                );
        
        if (ObjectUtil.isNotNull(excludeMeetingId)) {
            wrapper.ne(Meeting::getId, excludeMeetingId);
        }
        
        long conflictCount = count(wrapper);
        if (conflictCount > 0) {
            throw new BusinessException("该时间段内会议室已被占用");
        }
    }
}
