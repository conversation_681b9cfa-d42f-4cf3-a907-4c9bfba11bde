package com.meeting.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.meeting.common.enums.ParticipantStatus;
import com.meeting.entity.MeetingParticipant;
import com.meeting.exception.BusinessException;
import com.meeting.mapper.MeetingParticipantMapper;
import com.meeting.service.MeetingParticipantService;
import com.meeting.vo.MeetingParticipantVO;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 会议参与者服务实现类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MeetingParticipantServiceImpl extends ServiceImpl<MeetingParticipantMapper, MeetingParticipant> implements MeetingParticipantService {

    @Override
    public List<MeetingParticipantVO> getParticipantsByMeetingId(Long meetingId) {
        if (ObjectUtil.isNull(meetingId)) {
            throw new BusinessException("会议ID不能为空");
        }
        
        List<MeetingParticipantVO> participants = baseMapper.selectParticipantsByMeetingId(meetingId);
        
        // 设置状态描述
        participants.forEach(participant -> {
            ParticipantStatus status = ParticipantStatus.getByCode(participant.getStatus());
            if (ObjectUtil.isNotNull(status)) {
                participant.setStatusDesc(status.getDesc());
            }
        });
        
        return participants;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean addParticipants(Long meetingId, List<Long> participantIds) {
        if (ObjectUtil.isNull(meetingId)) {
            throw new BusinessException("会议ID不能为空");
        }
        
        if (ObjectUtil.isEmpty(participantIds)) {
            return true;
        }
        
        List<MeetingParticipant> participants = new ArrayList<>();
        for (Long userId : participantIds) {
            // 检查是否已经是参与者
            LambdaQueryWrapper<MeetingParticipant> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(MeetingParticipant::getMeetingId, meetingId)
                    .eq(MeetingParticipant::getUserId, userId);
            
            MeetingParticipant existParticipant = baseMapper.selectOne(wrapper);
            if (ObjectUtil.isNull(existParticipant)) {
                MeetingParticipant participant = new MeetingParticipant();
                participant.setMeetingId(meetingId);
                participant.setUserId(userId);
                participant.setRole("PARTICIPANT");
                participant.setStatus(ParticipantStatus.PENDING.getCode());
                participant.setInvitedTime(LocalDateTime.now());
                participants.add(participant);
            }
        }
        
        if (!participants.isEmpty()) {
            return saveBatch(participants);
        }
        
        return true;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean removeParticipants(Long meetingId, List<Long> participantIds) {
        if (ObjectUtil.isNull(meetingId)) {
            throw new BusinessException("会议ID不能为空");
        }
        
        if (ObjectUtil.isEmpty(participantIds)) {
            return true;
        }
        
        LambdaQueryWrapper<MeetingParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MeetingParticipant::getMeetingId, meetingId)
                .in(MeetingParticipant::getUserId, participantIds);
        
        return remove(wrapper);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateParticipantStatus(Long meetingId, Long userId, String status) {
        if (ObjectUtil.isNull(meetingId) || ObjectUtil.isNull(userId)) {
            throw new BusinessException("会议ID和用户ID不能为空");
        }
        
        // 验证状态是否有效
        ParticipantStatus participantStatus = ParticipantStatus.getByCode(status);
        if (ObjectUtil.isNull(participantStatus)) {
            throw new BusinessException("无效的参会状态");
        }
        
        LambdaQueryWrapper<MeetingParticipant> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MeetingParticipant::getMeetingId, meetingId)
                .eq(MeetingParticipant::getUserId, userId);
        
        MeetingParticipant participant = baseMapper.selectOne(wrapper);
        if (ObjectUtil.isNull(participant)) {
            throw new BusinessException("参与者不存在");
        }
        
        participant.setStatus(status);
        participant.setResponseTime(LocalDateTime.now());
        
        return updateById(participant);
    }

    @Override
    public MeetingParticipantVO getParticipantStatus(Long meetingId, Long userId) {
        if (ObjectUtil.isNull(meetingId) || ObjectUtil.isNull(userId)) {
            throw new BusinessException("会议ID和用户ID不能为空");
        }
        
        MeetingParticipantVO participant = baseMapper.selectParticipantByUserIdAndMeetingId(userId, meetingId);
        
        if (ObjectUtil.isNotNull(participant)) {
            ParticipantStatus status = ParticipantStatus.getByCode(participant.getStatus());
            if (ObjectUtil.isNotNull(status)) {
                participant.setStatusDesc(status.getDesc());
            }
        }
        
        return participant;
    }
}
