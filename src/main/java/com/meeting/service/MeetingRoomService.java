package com.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meeting.entity.MeetingRoom;
import com.meeting.vo.MeetingRoomVO;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议室服务接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
public interface MeetingRoomService extends IService<MeetingRoom> {

    /**
     * 获取所有会议室列表
     *
     * @return 会议室列表
     */
    List<MeetingRoomVO> getAllRooms();

    /**
     * 获取可用的会议室列表
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 可用的会议室列表
     */
    List<MeetingRoomVO> getAvailableRooms(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取会议室详情
     *
     * @param id 会议室ID
     * @return 会议室详情
     */
    MeetingRoomVO getRoomDetail(Long id);
}