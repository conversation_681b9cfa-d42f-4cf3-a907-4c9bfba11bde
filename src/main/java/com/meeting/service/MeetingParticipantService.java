package com.meeting.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.meeting.entity.MeetingParticipant;
import com.meeting.vo.MeetingParticipantVO;

import java.util.List;

/**
 * 会议参与者服务接口
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
public interface MeetingParticipantService extends IService<MeetingParticipant> {

    /**
     * 根据会议ID获取参与者列表
     *
     * @param meetingId 会议ID
     * @return 参与者列表
     */
    List<MeetingParticipantVO> getParticipantsByMeetingId(Long meetingId);

    /**
     * 添加会议参与者
     *
     * @param meetingId      会议ID
     * @param participantIds 参与者ID列表
     * @return 是否添加成功
     */
    Boolean addParticipants(Long meetingId, List<Long> participantIds);

    /**
     * 移除会议参与者
     *
     * @param meetingId      会议ID
     * @param participantIds 参与者ID列表
     * @return 是否移除成功
     */
    Boolean removeParticipants(Long meetingId, List<Long> participantIds);

    /**
     * 更新参会状态
     *
     * @param meetingId 会议ID
     * @param userId    用户ID
     * @param status    参会状态
     * @return 是否更新成功
     */
    Boolean updateParticipantStatus(Long meetingId, Long userId, String status);

    /**
     * 获取用户的参会状态
     *
     * @param meetingId 会议ID
     * @param userId    用户ID
     * @return 参会状态
     */
    MeetingParticipantVO getParticipantStatus(Long meetingId, Long userId);
}
