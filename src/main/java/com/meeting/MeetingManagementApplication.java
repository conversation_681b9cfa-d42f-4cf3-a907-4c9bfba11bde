package com.meeting;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 会议管理系统启动类
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@SpringBootApplication
@MapperScan("com.meeting.mapper")
public class MeetingManagementApplication {

    public static void main(String[] args) {
        SpringApplication.run(MeetingManagementApplication.class, args);
        System.out.println("=================================");
        System.out.println("  会议管理系统启动成功！");
        System.out.println("  接口文档地址：http://localhost:8080/api/doc.html");
        System.out.println("  Druid监控地址：http://localhost:8080/api/druid");
        System.out.println("=================================");
    }
}
