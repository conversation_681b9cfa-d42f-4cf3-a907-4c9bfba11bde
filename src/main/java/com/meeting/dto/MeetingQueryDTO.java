package com.meeting.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 会议查询DTO
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@Schema(description = "会议查询参数")
public class MeetingQueryDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 当前页
     */
    @Schema(description = "当前页", example = "1")
    private Long current = 1L;

    /**
     * 页面大小
     */
    @Schema(description = "页面大小", example = "10")
    private Long size = 10L;

    /**
     * 会议标题
     */
    @Schema(description = "会议标题", example = "项目")
    private String title;

    /**
     * 会议室ID
     */
    @Schema(description = "会议室ID", example = "1")
    private Long roomId;

    /**
     * 组织者ID
     */
    @Schema(description = "组织者ID", example = "1")
    private Long organizerId;

    /**
     * 会议状态
     */
    @Schema(description = "会议状态", example = "SCHEDULED")
    private String status;

    /**
     * 会议类型
     */
    @Schema(description = "会议类型", example = "INTERNAL")
    private String type;

    /**
     * 开始时间（查询起始）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间（查询起始）", example = "2025-08-01 00:00:00")
    private LocalDateTime startTimeFrom;

    /**
     * 开始时间（查询截止）
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间（查询截止）", example = "2025-08-31 23:59:59")
    private LocalDateTime startTimeTo;
}