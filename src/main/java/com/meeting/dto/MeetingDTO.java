package com.meeting.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.Future;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 会议请求DTO
 *
 * <AUTHOR> Agent
 * @since 2025-07-30
 */
@Data
@Schema(description = "会议请求参数")
public class MeetingDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 会议ID（更新时传入）
     */
    @Schema(description = "会议ID", example = "1")
    private Long id;

    /**
     * 会议标题
     */
    @NotBlank(message = "会议标题不能为空")
    @Schema(description = "会议标题", required = true, example = "项目启动会议")
    private String title;

    /**
     * 会议描述
     */
    @Schema(description = "会议描述", example = "讨论新项目的启动和规划")
    private String description;

    /**
     * 开始时间
     */
    @NotNull(message = "开始时间不能为空")
    @Future(message = "开始时间必须是未来时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "开始时间", required = true, example = "2025-08-01 09:00:00")
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    @NotNull(message = "结束时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Schema(description = "结束时间", required = true, example = "2025-08-01 11:00:00")
    private LocalDateTime endTime;

    /**
     * 会议室ID
     */
    @NotNull(message = "会议室不能为空")
    @Schema(description = "会议室ID", required = true, example = "1")
    private Long roomId;

    /**
     * 会议类型
     */
    @Schema(description = "会议类型", example = "INTERNAL")
    private String type;

    /**
     * 会议议程
     */
    @Schema(description = "会议议程", example = "1. 项目背景介绍\n2. 项目目标确定")
    private String agenda;

    /**
     * 参会人员ID列表
     */
    @Schema(description = "参会人员ID列表", example = "[2, 3, 4]")
    private List<Long> participantIds;
}