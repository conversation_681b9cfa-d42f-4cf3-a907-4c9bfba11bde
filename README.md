# 会议管理系统后端

## 技术栈

- **Spring Boot 3.2.1** - 基础框架
- **MyBatis Plus 3.5.5** - ORM框架
- **Druid 1.2.20** - 数据源
- **Sa-Token 1.37.0** - 权限认证
- **MySQL 8.0** - 数据库
- **Redis** - 缓存
- **Knife4j 4.4.0** - API文档
- **Hutool 5.8.25** - 工具类

## 项目结构

```
src/main/java/com/meeting/
├── config/          # 配置类
├── controller/      # 控制器层
├── service/         # 服务层
├── mapper/          # 数据访问层
├── entity/          # 实体类
├── dto/             # 数据传输对象
├── vo/              # 视图对象
├── common/          # 通用类
├── exception/       # 异常处理
└── utils/           # 工具类
```

## 环境要求

- **JDK 17+**
- **Maven 3.6+**
- **MySQL 8.0+**
- **Redis 5.0+**

## 快速开始

### 1. 数据库准备

1. 创建MySQL数据库：
```sql
CREATE DATABASE meeting_management CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

2. 执行初始化脚本：
```bash
mysql -u root -p meeting_management < src/main/resources/sql/init.sql
```

### 2. 配置文件

修改 `src/main/resources/application.yml` 中的数据库和Redis连接信息：

```yaml
spring:
  datasource:
    url: ************************************************************************************************************************************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

### 3. 启动应用

```bash
# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run
```

或者直接运行主类 `MeetingManagementApplication`

### 4. 访问应用

- **应用地址**: http://localhost:8080/api
- **API文档**: http://localhost:8080/api/doc.html
- **Druid监控**: http://localhost:8080/api/druid (admin/123456)

## API文档

启动应用后，访问 http://localhost:8080/api/doc.html 查看完整的API文档。

### 主要接口模块

1. **认证管理** (`/auth`)
   - 用户登录
   - 用户注册
   - 用户退出
   - 获取当前用户信息

2. **用户管理** (`/users`)
   - 获取用户列表
   - 批量获取用户信息

3. **会议管理** (`/meetings`)
   - 创建会议
   - 更新会议
   - 删除会议
   - 分页查询会议
   - 会议状态管理（开始/结束/取消）
   - 参与者管理

4. **会议室管理** (`/rooms`)
   - 获取会议室列表
   - 获取可用会议室
   - 会议室详情查询

## 默认账户

系统初始化时会创建以下默认账户：

| 用户名 | 密码 | 角色 | 描述 |
|--------|------|------|------|
| admin | 123456 | 管理员 | 系统管理员 |
| zhangsan | 123456 | 普通用户 | 技术部员工 |
| lisi | 123456 | 普通用户 | 产品部员工 |
| wangwu | 123456 | 普通用户 | 市场部员工 |
| zhaoliu | 123456 | 普通用户 | 人事部员工 |

## 开发指南

### 添加新的API接口

1. 在对应的Controller中添加接口方法
2. 添加Swagger注解进行API文档说明
3. 在Service层实现业务逻辑
4. 必要时添加新的DTO和VO类

### 数据库操作

- 使用MyBatis Plus进行数据库操作
- 复杂查询在Mapper接口中使用@Select注解
- 实体类继承BaseEntity获得通用字段

### 权限控制

- 使用Sa-Token进行权限认证
- 在需要登录的接口上会自动进行token验证
- 可以通过注解进行更细粒度的权限控制

## 部署说明

### 打包应用

```bash
mvn clean package -DskipTests
```

生成的jar包位于 `target/meeting-management-backend-1.0.0.jar`

### 运行jar包

```bash
java -jar target/meeting-management-backend-1.0.0.jar
```

### Docker部署

1. 创建Dockerfile：
```dockerfile
FROM openjdk:17-jre-slim
COPY target/meeting-management-backend-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

2. 构建镜像：
```bash
docker build -t meeting-management-backend .
```

3. 运行容器：
```bash
docker run -d -p 8080:8080 --name meeting-backend meeting-management-backend
```

## 配置说明

### 主要配置项

- `server.port`: 应用端口
- `spring.datasource`: 数据库连接配置
- `spring.redis`: Redis连接配置
- `sa-token`: 认证相关配置
- `mybatis-plus`: ORM配置
- `knife4j`: API文档配置

### 生产环境配置

建议在生产环境中：

1. 修改默认密码
2. 配置SSL证书
3. 调整数据库连接池参数
4. 配置日志级别
5. 设置合适的JVM参数

## 故障排除

### 常见问题

1. **启动失败**
   - 检查数据库连接配置
   - 确认MySQL和Redis服务已启动
   - 查看日志文件排查具体错误

2. **接口404错误**
   - 确认请求路径是否正确
   - 检查Controller的RequestMapping配置

3. **认证失败**
   - 确认token是否正确传递
   - 检查Sa-Token配置

### 日志查看

应用日志会输出到控制台，生产环境建议配置日志文件：

```yaml
logging:
  file:
    name: logs/meeting-management.log
  level:
    com.meeting: debug
```
